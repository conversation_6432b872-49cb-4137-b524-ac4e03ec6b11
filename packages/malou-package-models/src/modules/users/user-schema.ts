import { ApplicationLanguage, emailRegex, Role, UserCaslRole } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const userJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'User',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        name: {
            type: 'string',
        },
        organizationIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
            },
            ref: 'Organization',
        },
        lastname: {
            type: 'string',
            trim: true,
        },
        role: {
            enum: Object.values(Role),
            default: Role.MALOU_GUEST,
            lowercase: true,
        },
        caslRole: {
            enum: Object.values(UserCaslRole),
            default: UserCaslRole.GUEST,
        },
        password: {
            type: 'string',
            trim: true,
            select: false,
            minLength: 6,
        },
        email: {
            type: 'string',
            match: emailRegex,
        },
        phone: {
            type: 'object',
            additionalProperties: false,
            properties: {
                prefix: {
                    type: 'number',
                },
                digits: {
                    type: 'number',
                },
            },
        },
        // This field is related to the external provider which creates the principal user i.e. the first user of the organization
        // It should be Hyperline or Airtable for example
        // It can be null for users created directly in the MalouApp
        subscriptionsProviderId: {
            type: 'string',
            description: 'External provider ID (e.g., Hyperline customer ID)',
        },
        // This field is used to check if the user has verified his email.
        // As it was created in the past, it also serves for some aggregations to check if a user is still active in the sense of Malou
        verified: {
            type: 'boolean',
            default: true,
        },
        // This field was necessary because verified served two purposes: one to tell if the user has verified email, one to tell if the user had been deactivated
        // We had an issue in the user list view where we wanted not to show deactivated users but we wanted to show users that had not verified their email
        // So we created this field to separate the two concepts
        hasBeenDeactivatedByAdmin: {
            type: 'boolean',
            default: false,
        },
        defaultLanguage: {
            enum: Object.values(ApplicationLanguage),
            default: ApplicationLanguage.FR,
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        settings: {
            $ref: '#/definitions/Settings',
            default: {
                receiveFeedbacks: true,
                notifications: {
                    email: {
                        reviewReplyReminder: {
                            active: true,
                        },
                        specialHourReminder: {
                            active: true,
                        },
                        postSuggestion: {
                            active: true,
                        },
                        roiActivated: {
                            active: true,
                        },
                        postError: {
                            active: true,
                        },
                        summary: {
                            active: true,
                        },
                        platformDisconnected: {
                            active: true,
                        },
                    },
                    web: {
                        showFloatingToast: true,
                        filters: {
                            restaurantIds: [],
                        },
                        newReviews: {
                            active: true,
                        },
                        reviewReplyReminder: {
                            active: true,
                        },
                        specialHourReminder: {
                            active: true,
                        },
                        postSuggestion: {
                            active: true,
                        },
                        newMessage: {
                            active: true,
                        },
                        roiActivated: {
                            active: true,
                        },
                        postError: {
                            active: true,
                        },
                        platformDisconnected: {
                            active: true,
                        },
                        informationUpdateError: {
                            active: true,
                        },
                    },
                    mobile: {
                        userDevicesTokens: [],
                        newMessage: {
                            active: true,
                            realtime: true,
                            receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                        },
                        active: true,
                        newReviews: {
                            active: true,
                            realtime: true,
                            receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                            concernedRatings: [1, 2, 3, 4, 5],
                            includeAutoRepliedReviews: true,
                        },
                        noMoreScheduledPosts: {
                            active: true,
                        },
                    },
                },
                notificationSettings: {
                    userDevicesTokens: [],
                    active: true,
                    reviews: {
                        active: true,
                        realtime: true,
                        receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                        concernedRatings: [1, 2, 3, 4, 5],
                        includeAutoRepliedReviews: true,
                    },
                    messages: {
                        active: true,
                        realtime: true,
                        receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                    },
                    posts: {
                        noMoreScheduledPosts: {
                            active: true,
                        },
                        publishError: {
                            active: true,
                        },
                    },
                },
                receiveMessagesNotifications: {
                    active: true,
                    restaurantsIds: [],
                },
            },
        },
        hasV3Access: {
            type: 'boolean',
            default: true,
        },
        expireSessionBefore: {
            type: 'string',
            format: 'date-time',
        },
        profilePicture: {
            type: 'string',
            format: 'objectId',
            ref: 'Media',
        },
        shouldExpireAbilitySession: {
            type: 'boolean',
            default: false,
        },
        createdByUserId: {
            anyOf: [
                {
                    type: 'string',
                    format: 'objectId',
                },
                {
                    type: 'null',
                },
            ],
        },
        lastVisitedRestaurantId: {
            type: 'string',
            format: 'objectId',
            nullable: true,
        },
    },
    required: [
        '_id',
        'caslRole',
        'createdAt',
        'email',
        'hasV3Access',
        'organizationIds',
        'password',
        'role',
        'settings',
        'updatedAt',
        'hasBeenDeactivatedByAdmin',
        'verified',
        'defaultLanguage',
    ],
    definitions: {
        Settings: {
            type: 'object',
            additionalProperties: false,
            properties: {
                receiveFeedbacks: {
                    type: 'boolean',
                },
                notifications: {
                    $ref: '#/definitions/Notifications',
                },
                notificationSettings: {
                    $ref: '#/definitions/NotificationSettings',
                },
                receiveMessagesNotifications: {
                    $ref: '#/definitions/ReceiveMessagesNotifications',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
            },
            required: ['notificationSettings', 'receiveFeedbacks', 'receiveMessagesNotifications', 'notifications'],
            title: 'Settings',
        },
        Notifications: {
            type: 'object',
            additionalProperties: false,
            properties: {
                email: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        reviewReplyReminder: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        specialHourReminder: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        postSuggestion: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        postError: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        roiActivated: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        summary: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        platformDisconnected: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                    },
                    required: [
                        'reviewReplyReminder',
                        'specialHourReminder',
                        'postSuggestion',
                        'postError',
                        'roiActivated',
                        'summary',
                        'platformDisconnected',
                    ],
                },
                web: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        showFloatingToast: {
                            type: 'boolean',
                            default: true,
                        },
                        filters: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                restaurantIds: {
                                    type: 'array',
                                    items: {
                                        type: 'string',
                                        format: 'objectId',
                                    },
                                    default: [],
                                },
                            },
                            required: ['restaurantIds'],
                        },
                        newReviews: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        reviewReplyReminder: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        specialHourReminder: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        postSuggestion: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        postError: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        newMessage: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        roiActivated: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        platformDisconnected: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                        informationUpdateError: {
                            type: 'object',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active'],
                        },
                    },
                    required: [
                        'newReviews',
                        'reviewReplyReminder',
                        'specialHourReminder',
                        'postSuggestion',
                        'postError',
                        'roiActivated',
                        'newMessage',
                        'showFloatingToast',
                        'filters',
                        'platformDisconnected',
                        'informationUpdateError',
                    ],
                },
                mobile: {
                    type: 'object',
                    additionalProperties: false,
                    description: 'Mobile push notification settings',
                    properties: {
                        userDevicesTokens: {
                            type: 'array',
                            description: 'Array of valid push notification tokens for user devices',
                            items: {
                                type: 'string',
                            },
                        },
                        active: {
                            type: 'boolean',
                            description: 'Master switch for all mobile notifications',
                        },
                        newMessage: {
                            type: 'object',
                            description: 'Settings for new message notifications',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                                realtime: {
                                    type: 'boolean',
                                },
                                receivingWeekDays: {
                                    type: 'array',
                                    items: {
                                        type: 'integer',
                                    },
                                },
                            },
                            required: ['active', 'realtime', 'receivingWeekDays'],
                        },
                        newReviews: {
                            type: 'object',
                            description: 'Settings for new review notifications',
                            additionalProperties: false,
                            properties: {
                                active: {
                                    type: 'boolean',
                                },
                                realtime: {
                                    type: 'boolean',
                                },
                                receivingWeekDays: {
                                    type: 'array',
                                    items: {
                                        type: 'integer',
                                    },
                                },
                                concernedRatings: {
                                    type: 'array',
                                    items: {
                                        type: 'integer',
                                    },
                                },
                                includeAutoRepliedReviews: {
                                    type: 'boolean',
                                },
                            },
                            required: ['active', 'realtime', 'receivingWeekDays', 'concernedRatings', 'includeAutoRepliedReviews'],
                        },
                        noMoreScheduledPosts: {
                            type: 'object',
                            description: 'Settings for no more scheduled posts notifications',
                            additionalProperties: false,
                            properties: {
                                active: { type: 'boolean' },
                            },
                            required: ['active'],
                            title: 'NoMoreScheduledPosts',
                        },
                    },
                    required: ['newMessage', 'userDevicesTokens', 'active', 'newReviews', 'noMoreScheduledPosts'],
                },
            },
            required: ['email', 'web', 'mobile'],
            title: 'Notifications',
        },
        NotificationSettings: {
            type: 'object',
            additionalProperties: false,
            properties: {
                userDevicesTokens: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
                active: {
                    type: 'boolean',
                },
                reviews: {
                    $ref: '#/definitions/Reviews',
                },
                messages: {
                    $ref: '#/definitions/Messages',
                },
                posts: {
                    $ref: '#/definitions/Posts',
                },
            },
            required: ['active', 'messages', 'reviews', 'posts', 'userDevicesTokens'],
            title: 'NotificationSettings',
        },
        Messages: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                },
                realtime: {
                    type: 'boolean',
                },
                receivingWeekDays: {
                    type: 'array',
                    items: {
                        type: 'integer',
                    },
                },
            },
            required: ['active', 'realtime', 'receivingWeekDays'],
            title: 'Messages',
        },
        Reviews: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                },
                realtime: {
                    type: 'boolean',
                },
                receivingWeekDays: {
                    type: 'array',
                    items: {
                        type: 'integer',
                    },
                },
                concernedRatings: {
                    type: 'array',
                    items: {
                        type: 'integer',
                    },
                },
                includeAutoRepliedReviews: {
                    type: 'boolean',
                },
            },
            required: ['active', 'concernedRatings', 'includeAutoRepliedReviews', 'realtime', 'receivingWeekDays'],
            title: 'Reviews',
        },
        ReceiveMessagesNotifications: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                },
                restaurantsIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                },
            },
            required: ['active', 'restaurantsIds'],
            title: 'ReceiveMessagesNotifications',
        },
        Posts: {
            type: 'object',
            additionalProperties: false,
            properties: {
                noMoreScheduledPosts: {
                    $ref: '#/definitions/NoMoreScheduledPosts',
                },
                publishError: {
                    $ref: '#/definitions/PublishError',
                },
            },
            required: ['noMoreScheduledPosts', 'publishError'],
            title: 'Posts',
        },
        NoMoreScheduledPosts: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                    default: true,
                },
                lastNotificationSentDate: {
                    type: 'string',
                    format: 'date-time',
                    default: Date.now,
                },
            },
            required: ['active', 'lastNotificationSentDate'],
            title: 'NoMoreScheduledPosts',
        },
        PublishError: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                    default: true,
                },
            },
            required: ['active'],
            title: 'PublishError',
        },
    },
} as const satisfies JSONSchemaExtraProps;
