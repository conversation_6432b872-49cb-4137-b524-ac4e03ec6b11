/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async execute() {
        await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
            organizationId: '61dc14920b4fcb4eea6b2fd1',
            status: StoreLocatorJobStatus.PENDING,
        });
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
